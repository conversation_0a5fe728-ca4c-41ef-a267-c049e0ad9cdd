"""
Project management API endpoints.
"""

from typing import List

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, status
from sqlalchemy.orm import Session

from ...api.v1.auth import get_current_active_user
from ...core.database import get_db
from ...models import Project as ProjectModel
from ...schemas import (
    Document,
    DuplicateFileInfo,
    DuplicateHandlingRequest,
    Project,
    ProjectCreate,
    ProjectUpdate,
    Requirement,
)
from ...services.document_service import DocumentService
from ...services.gap_analysis_service import GapAnalysisService
from ...utils.logger import logger

router = APIRouter()


from pydantic import BaseModel


class ProcessGapAnalysisDocument(BaseModel):
    project_id: int
    filename: str
    gap_analysis_uploaded_file_data: list


@router.post("/", response_model=Project, status_code=status.HTTP_201_CREATED)
async def create_project(
    project_data: ProjectCreate,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Create a new project.

    Args:
        project_data: Project creation data
        current_user: Current authenticated user
        db: Database session

    Returns:
        Created project
    """
    project = ProjectModel(
        name=project_data.name,
        description=project_data.description,
        owner_id=current_user.id,
    )

    db.add(project)
    db.commit()
    db.refresh(project)

    logger.info(f"Project created: {project.name} by user {current_user.username}")
    return project


@router.get("/", response_model=List[Project])
async def get_user_projects(
    current_user=Depends(get_current_active_user), db: Session = Depends(get_db)
):
    """
    Get all projects for the current user.

    Args:
        current_user: Current authenticated user
        db: Database session

    Returns:
        List of user's projects
    """
    projects = (
        db.query(ProjectModel).filter(ProjectModel.owner_id == current_user.id).all()
    )
    return projects


@router.get("/{project_id}", response_model=Project)
async def get_project(
    project_id: int,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Get a specific project.

    Args:
        project_id: Project ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        Project details
    """
    project = (
        db.query(ProjectModel)
        .filter(ProjectModel.id == project_id, ProjectModel.owner_id == current_user.id)
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )

    return project


@router.put("/{project_id}", response_model=Project)
async def update_project(
    project_id: int,
    project_update: ProjectUpdate,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Update a project.

    Args:
        project_id: Project ID
        project_update: Project update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        Updated project
    """
    project = (
        db.query(ProjectModel)
        .filter(ProjectModel.id == project_id, ProjectModel.owner_id == current_user.id)
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )

    update_data = project_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(project, field, value)

    db.commit()
    db.refresh(project)

    logger.info(f"Project updated: {project.name} by user {current_user.username}")
    return project


@router.post("/{project_id}/documents/check-duplicate")
async def check_duplicate_file(
    project_id: int,
    file: UploadFile = File(...),
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Check for duplicate files before upload.

    Returns:
        Duplicate information if found, None otherwise
    """
    # Verify project ownership
    project = (
        db.query(ProjectModel)
        .filter(ProjectModel.id == project_id, ProjectModel.owner_id == current_user.id)
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )

    # Read file content and calculate hash
    file_content = await file.read()
    file_hash = DocumentService.calculate_file_hash(file_content)

    # Check for duplicates
    hash_duplicate = DocumentService.check_duplicate_hash(db, project_id, file_hash)
    filename_duplicate = DocumentService.check_duplicate_filename(
        db, project_id, file.filename
    )

    result = {
        "has_duplicate": False,
        "hash_duplicate": None,
        "filename_duplicate": None,
    }

    if hash_duplicate:
        result["has_duplicate"] = True
        result["hash_duplicate"] = DocumentService.get_duplicate_info(
            db, hash_duplicate
        )

    if filename_duplicate and (
        not hash_duplicate or filename_duplicate.id != hash_duplicate.id
    ):
        result["has_duplicate"] = True
        result["filename_duplicate"] = DocumentService.get_duplicate_info(
            db, filename_duplicate
        )

    return result


@router.post(
    "/{project_id}/documents",
    response_model=Document,
    status_code=status.HTTP_201_CREATED,
)
async def upload_document(
    project_id: int,
    file: UploadFile = File(...),
    duplicate_handling: DuplicateHandlingRequest = None,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Upload a document to a project with duplicate handling.

    Args:
        project_id: Project ID
        file: Uploaded file
        duplicate_handling: How to handle duplicates
        current_user: Current authenticated user
        db: Database session

    Returns:
        Uploaded document information
    """
    # Handle duplicate replacement if specified
    if (
        duplicate_handling
        and duplicate_handling.action == "replace"
        and duplicate_handling.existing_document_id
    ):
        DocumentService.delete_document_cascade(
            db, duplicate_handling.existing_document_id, current_user.id
        )

    document = await DocumentService.upload_document(
        db, file, project_id, current_user.id
    )
    logger.info(f"Document uploaded: {document.filename} to project {project_id}")
    return document


@router.get("/{project_id}/documents", response_model=List[Document])
async def get_project_documents(
    project_id: int,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Get all documents for a project.

    Args:
        project_id: Project ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        List of project documents
    """
    documents = DocumentService.get_project_documents(db, project_id, current_user.id)
    return documents


@router.post(
    "/{project_id}/documents/{document_id}/process", response_model=List[Requirement]
)
async def process_document(
    project_id: int,
    document_id: int,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Process a document to extract requirements.

    Args:
        project_id: Project ID
        document_id: Document ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        List of extracted requirements
    """
    # Verify project ownership
    project = (
        db.query(ProjectModel)
        .filter(ProjectModel.id == project_id, ProjectModel.owner_id == current_user.id)
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )

    requirements = await DocumentService.process_document(db, document_id)
    logger.info(
        f"Document processed: {document_id}, found {len(requirements)} requirements"
    )
    return requirements


@router.post("/{project_id}/documents/{document_id}/regenerate-test-cases")
async def regenerate_test_cases(
    project_id: int,
    document_id: int,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Clear existing test cases for a document (for regeneration).
    """
    # Verify project ownership
    project = (
        db.query(ProjectModel)
        .filter(ProjectModel.id == project_id, ProjectModel.owner_id == current_user.id)
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )

    success = DocumentService.regenerate_test_cases_for_document(
        db, document_id, current_user.id
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
        )

    return {"message": "Test cases cleared for regeneration"}


@router.delete(
    "/{project_id}/documents/{document_id}", status_code=status.HTTP_204_NO_CONTENT
)
async def delete_document(
    project_id: int,
    document_id: int,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """Delete a document and its file from the filesystem."""
    success = DocumentService.delete_document_cascade(db, document_id, current_user.id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
        )

    logger.info(f"Document {document_id} deleted by user {current_user.username}")
    return  # 204 No Content response


@router.get(
    "/{project_id}/documents/{document_id}/requirements",
    response_model=List[Requirement],
)
async def get_document_requirements(
    project_id: int,
    document_id: int,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """Get requirements for a specific document."""
    # Verify project ownership
    project = (
        db.query(ProjectModel)
        .filter(ProjectModel.id == project_id, ProjectModel.owner_id == current_user.id)
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )

    requirements = DocumentService.get_document_requirements(db, document_id)
    return requirements


@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_project(
    project_id: int,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """Delete a project and all its associated data."""
    # Verify project ownership
    project = (
        db.query(ProjectModel)
        .filter(ProjectModel.id == project_id, ProjectModel.owner_id == current_user.id)
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found or access denied",
        )

    # Delete project (cascade will handle related data)
    db.delete(project)
    db.commit()

    logger.info(
        f"Project {project_id} ({project.name}) deleted by user {current_user.username}"
    )
    return  # 204 No Content response


@router.post(
    "/gap_analysis",
    status_code=status.HTTP_200_OK,
)
async def process_gap_analysis_document(data: ProcessGapAnalysisDocument):
    """
    Process a gap analysis document for generated test cases document.
    """
    try:
        service = GapAnalysisService()

        return service.process_gap_analysis_document(
            filename=data.filename,
            data=data.gap_analysis_uploaded_file_data,
        )

    except Exception as e:
        logger.error(f"Error processing document: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error processing document: {str(e)}",
        )
